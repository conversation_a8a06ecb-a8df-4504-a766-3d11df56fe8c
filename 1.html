<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特伦斯的个人小站</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #0f0f23;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 星空背景效果 */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: linear-gradient(180deg, #0f0f23 0%, #1a1a3e 50%, #2d1b69 100%);
        }

        /* 星星效果 */
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s ease-in-out infinite;
        }

        .star:nth-child(1) { width: 2px; height: 2px; top: 20%; left: 10%; animation-delay: 0s; }
        .star:nth-child(2) { width: 1px; height: 1px; top: 30%; left: 20%; animation-delay: 0.5s; }
        .star:nth-child(3) { width: 3px; height: 3px; top: 10%; left: 30%; animation-delay: 1s; }
        .star:nth-child(4) { width: 2px; height: 2px; top: 40%; left: 40%; animation-delay: 1.5s; }
        .star:nth-child(5) { width: 1px; height: 1px; top: 15%; left: 50%; animation-delay: 2s; }
        .star:nth-child(6) { width: 2px; height: 2px; top: 25%; left: 60%; animation-delay: 2.5s; }
        .star:nth-child(7) { width: 3px; height: 3px; top: 35%; left: 70%; animation-delay: 3s; }
        .star:nth-child(8) { width: 1px; height: 1px; top: 45%; left: 80%; animation-delay: 0.3s; }
        .star:nth-child(9) { width: 2px; height: 2px; top: 5%; left: 90%; animation-delay: 0.8s; }
        .star:nth-child(10) { width: 1px; height: 1px; top: 50%; left: 15%; animation-delay: 1.3s; }
        .star:nth-child(11) { width: 2px; height: 2px; top: 60%; left: 25%; animation-delay: 1.8s; }
        .star:nth-child(12) { width: 3px; height: 3px; top: 70%; left: 35%; animation-delay: 2.3s; }
        .star:nth-child(13) { width: 1px; height: 1px; top: 80%; left: 45%; animation-delay: 2.8s; }
        .star:nth-child(14) { width: 2px; height: 2px; top: 65%; left: 55%; animation-delay: 0.2s; }
        .star:nth-child(15) { width: 1px; height: 1px; top: 75%; left: 65%; animation-delay: 0.7s; }
        .star:nth-child(16) { width: 3px; height: 3px; top: 85%; left: 75%; animation-delay: 1.2s; }
        .star:nth-child(17) { width: 2px; height: 2px; top: 55%; left: 85%; animation-delay: 1.7s; }
        .star:nth-child(18) { width: 1px; height: 1px; top: 90%; left: 95%; animation-delay: 2.2s; }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* 流星效果 */
        .shooting-star {
            position: fixed;
            width: 2px;
            height: 2px;
            background: linear-gradient(45deg, #fff, transparent);
            border-radius: 50%;
            z-index: -1;
            animation: shoot 8s linear infinite;
        }

        .shooting-star:nth-child(19) {
            top: 10%;
            left: -10px;
            animation-delay: 0s;
        }

        .shooting-star:nth-child(20) {
            top: 30%;
            left: -10px;
            animation-delay: 4s;
        }

        @keyframes shoot {
            0% {
                transform: translateX(0) translateY(0);
                opacity: 1;
            }
            70% {
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(50vh);
                opacity: 0;
            }
        }

        /* 极光效果 */
        .aurora {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.3;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(0, 255, 150, 0.1) 50%,
                rgba(0, 150, 255, 0.1) 70%,
                transparent 90%
            );
            animation: aurora 15s ease-in-out infinite;
        }

        @keyframes aurora {
            0%, 100% {
                transform: translateX(-100px) skewX(0deg);
                opacity: 0.2;
            }
            50% {
                transform: translateX(100px) skewX(10deg);
                opacity: 0.4;
            }
        }

        /* 主容器 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        /* 头部导航 */
        .header {
            padding: 20px 0;
            animation: slideDown 1s ease-out;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 50px;
            padding: 15px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            transform: translateY(-2px);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: white;
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* 主要内容区域 */
        .hero {
            text-align: center;
            padding: 100px 0;
            animation: fadeInUp 1.2s ease-out 0.3s both;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3); }
            to { text-shadow: 0 4px 30px rgba(255, 255, 255, 0.4); }
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 60px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
        }

        .btn-primary:hover {
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
        }

        /* 特色卡片区域 */
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 80px;
            animation: fadeInUp 1.2s ease-out 0.6s both;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
            margin-bottom: 15px;
        }

        .feature-desc {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        /* 底部区域 */
        .footer {
            text-align: center;
            padding: 40px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            animation: fadeIn 1.5s ease-out 0.9s both;
        }

        .footer-content {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 20px;
        }

        .icp-info {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            display: inline-block;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 动画定义 */
        @keyframes slideDown {
            from { transform: translateY(-100px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes fadeInUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 20px;
                padding: 20px;
            }

            .nav-links {
                gap: 20px;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .feature-card {
                padding: 30px 20px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }

            .hero {
                padding: 60px 0;
            }

            .hero-title {
                font-size: 2rem;
            }

            .btn {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 背景动画粒子 -->
    <div class="bg-animation">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <nav class="nav">
                <a href="#" class="logo">特伦斯的小站</a>
                <ul class="nav-links">
                    <li><a href="#home">首页</a></li>
                    <li><a href="#blog">博客</a></li>
                    <li><a href="#about">关于</a></li>
                    <li><a href="#contact">联系</a></li>
                </ul>
            </nav>
        </header>

        <!-- 主要内容区域 -->
        <main class="hero">
            <h1 class="hero-title">欢迎来到我的博客</h1>
            <p class="hero-subtitle">
                在这里分享技术心得、生活感悟和创意想法。<br>
                让我们一起探索知识的海洋，记录成长的足迹。
            </p>
            <div class="hero-buttons">
                <a href="#blog" class="btn btn-primary">开始阅读</a>
                <a href="#about" class="btn btn-secondary">了解更多</a>
            </div>
        </main>

        <!-- 特色功能卡片 -->
        <section class="features">
            <div class="feature-card">
                <span class="feature-icon">📝</span>
                <h3 class="feature-title">技术博客</h3>
                <p class="feature-desc">分享编程技巧、开发经验和最新技术趋势，助力你的技术成长之路。</p>
            </div>
            <div class="feature-card">
                <span class="feature-icon">💡</span>
                <h3 class="feature-title">创意分享</h3>
                <p class="feature-desc">记录灵感瞬间，分享创意想法，探讨设计理念和用户体验。</p>
            </div>
            <div class="feature-card">
                <span class="feature-icon">🌱</span>
                <h3 class="feature-title">成长记录</h3>
                <p class="feature-desc">记录学习历程，分享人生感悟，与你一起成长进步。</p>
            </div>
        </section>
    </div>

    <!-- 底部信息 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 特伦斯的个人小站. 用心记录，用爱分享.</p>
            </div>
            <div class="icp-info">
                京ICP备xxxxxx号-1 | 京公网安备xxxxxx号
            </div>
        </div>
    </footer>
</body>
</html>
